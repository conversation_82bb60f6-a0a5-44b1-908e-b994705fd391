import React, { useState } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { api } from '../../services/api'
import './index.scss'

function Test() {
  const [configData, setConfigData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  const handleTestConfig = async () => {
    console.log('开始测试配置接口')
    setLoading(true)
    setError('')
    try {
      const result = await api.home.getConfig()
      console.log('配置接口返回数据:', result)
      setConfigData(result)
    } catch (err: any) {
      console.error('配置接口调用失败:', err)
      setError(err.message || '接口调用失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <View className='test-page'>
      <View className='test-section'>
        <Text className='test-title'>API接口测试</Text>

        <View className='test-item'>
          <Text className='item-title'>首页配置接口测试</Text>
          <Button
            type='primary'
            onClick={handleTestConfig}
            disabled={loading}
          >
            {loading ? '测试中...' : '测试配置接口'}
          </Button>

          {error && (
            <View className='error-message'>
              <Text style={{color: 'red'}}>错误: {error}</Text>
            </View>
          )}

          {configData && (
            <View className='config-result'>
              <Text className='result-title'>接口返回数据:</Text>
              <View className='result-content'>
                <Text>{JSON.stringify(configData, null, 2)}</Text>
              </View>
            </View>
          )}
        </View>

        <View className='test-item'>
          <Text className='item-title'>接口地址</Text>
          <Text>http://localhost:3000/api/v1/miniprogram/configs/all</Text>
        </View>
      </View>
    </View>
  )
}

export default Test
