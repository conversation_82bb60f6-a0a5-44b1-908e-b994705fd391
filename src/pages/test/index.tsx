import React, { useState } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { Skeleton } from '@nutui/nutui-react-taro'
import { api } from '../../services/api'
import './index.scss'

function Test() {
  const [configData, setConfigData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [showSkeleton, setShowSkeleton] = useState(false)

  const handleTestConfig = async () => {
    console.log('开始测试配置接口')
    setLoading(true)
    setError('')
    try {
      const result = await api.home.getConfig()
      console.log('配置接口返回数据:', result)
      setConfigData(result)
    } catch (err: any) {
      console.error('配置接口调用失败:', err)
      setError(err.message || '接口调用失败')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleSkeleton = () => {
    setShowSkeleton(!showSkeleton)
  }

  return (
    <View className='test-page'>
      <View className='test-section'>
        <Text className='test-title'>API接口测试</Text>

        <View className='test-item'>
          <Text className='item-title'>首页配置接口测试</Text>
          <Button
            type='primary'
            onClick={handleTestConfig}
            disabled={loading}
          >
            {loading ? '测试中...' : '测试配置接口'}
          </Button>

          {error && (
            <View className='error-message'>
              <Text style={{color: 'red'}}>错误: {error}</Text>
            </View>
          )}

          {configData && (
            <View className='config-result'>
              <Text className='result-title'>接口返回数据:</Text>
              <View className='result-content'>
                <Text>{JSON.stringify(configData, null, 2)}</Text>
              </View>
            </View>
          )}
        </View>

        <View className='test-item'>
          <Text className='item-title'>接口地址</Text>
          <Text>http://localhost:3000/api/v1/miniprogram/configs/all</Text>
        </View>

        <View className='test-item'>
          <Text className='item-title'>Skeleton 骨架屏演示</Text>
          <Button
            type='default'
            onClick={handleToggleSkeleton}
          >
            {showSkeleton ? '隐藏骨架屏' : '显示骨架屏'}
          </Button>

          <View className='skeleton-demo'>
            {showSkeleton ? (
              <View className='skeleton-container'>
                <Text className='demo-title'>轮播图骨架屏:</Text>
                <Skeleton width="100%" height="160px" animated />

                <Text className='demo-title'>公告栏骨架屏:</Text>
                <Skeleton width="100%" height="40px" animated />

                <Text className='demo-title'>门店信息骨架屏:</Text>
                <View className='store-skeleton-demo'>
                  <Skeleton width="80px" height="80px" animated />
                  <View className='store-info-skeleton'>
                    <Skeleton width="60%" height="16px" animated />
                    <Skeleton width="40%" height="14px" animated />
                    <Skeleton width="80%" height="14px" animated />
                  </View>
                </View>
              </View>
            ) : (
              <View className='content-demo'>
                <Text className='demo-title'>正常内容显示</Text>
                <View className='demo-content'>
                  <Text>这里是正常的页面内容</Text>
                  <Text>当接口请求完成后会显示真实数据</Text>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  )
}

export default Test
