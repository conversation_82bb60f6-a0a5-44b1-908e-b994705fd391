.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .test-section {
    background: white;
    border-radius: 8px;
    padding: 20px;

    .test-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      display: block;
      text-align: center;
    }

    .test-item {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .item-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
        display: block;
      }

      .basic-test {
        .test-box {
          background: #f0f0f0;
          padding: 10px;
          border-radius: 4px;
          margin-top: 10px;
          color: #666;
        }
      }

      .error-message {
        margin-top: 10px;
        padding: 10px;
        background-color: #ffebee;
        border-radius: 4px;
      }

      .config-result {
        margin-top: 15px;

        .result-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 10px;
          display: block;
        }

        .result-content {
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          max-height: 300px;
          overflow-y: auto;

          text {
            font-size: 12px;
            font-family: monospace;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }

      .skeleton-demo {
        margin-top: 15px;

        .skeleton-container {
          .demo-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 15px 0 10px 0;
            display: block;
          }

          .store-skeleton-demo {
            display: flex;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 10px;

            .store-info-skeleton {
              flex: 1;
              margin-left: 15px;
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
        }

        .content-demo {
          .demo-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
            display: block;
          }

          .demo-content {
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;

            text {
              display: block;
              margin-bottom: 8px;
              color: #666;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}
