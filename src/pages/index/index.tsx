import { useState, useEffect } from "react";
import { View, Text, Image, Swiper, SwiperItem } from "@tarojs/components";
import {
  SearchBar,
  NoticeBar,
} from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import { api } from "../../services/api";
import UserService, { UserInfo } from "../../services/user";
import "./index.scss";

// 定义接口数据类型
interface NoticeItem {
  id: number;
  content: string;
  type?: string;
}

interface ContactInfo {
  phone: string;
  wechat: string;
  shareContent: {
    title: string;
    path: string;
    imageUrl?: string;
  };
}

interface BannerItem {
  id: number;
  image: string;
  title: string;
  subtitle: string;
  link?: string;
}

interface StoreInfo {
  id: number;
  name: string;
  image: string;
  salesPhone: string;
  salesWechat: string;
  recyclePhone: string;
  recycleWechat: string;
  address: string;
  isHot: boolean;
}

// API返回的门店数据结构
interface ApiStoreData {
  id: number;
  storeCode: string;
  storeName: string;
  province: string;
  city: string;
  district: string;
  detailedAddress: string;
  fullAddress: string;
  longitude: number | null;
  latitude: number | null;
  salesPhone: string;
  salesWechat: string;
  recyclePhone: string;
  recycleWechat: string;
  mainPhone: string;
  email: string | null;
  businessStatus: string;
  storeType: string;
  storeArea: number | null;
  employeeCount: number;
  serviceRadius: number;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// API响应结构
interface StoreApiResponse {
  success: boolean;
  data: {
    list: ApiStoreData[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}



function Index() {
  const [notices, setNotices] = useState<NoticeItem[]>([]);
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    phone: '***********',
    wechat: 'baofeng_recycle',
    shareContent: {
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index'
    }
  });
  const [banners, setBanners] = useState<BannerItem[]>([
    {
      id: 1,
      image: "https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png",
      title: "暴风数码",
      subtitle: "高价/秒/到/账",
    },
  ]);
  const [stores, setStores] = useState<StoreInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [showPhoneAuthModal, setShowPhoneAuthModal] = useState(false);

  // 获取首页配置数据
  const fetchHomeConfig = async () => {
    try {
      setLoading(true);
      const config = await api.home.getConfig();
      console.log('首页配置数据:', config);

      // 处理公告数据
      if (config && config.notices && Array.isArray(config.notices)) {
        setNotices(config.notices);
      } else if (config && config.announcements && Array.isArray(config.announcements)) {
        // 兼容不同的字段名
        const formattedNotices = config.announcements.map((item: any, index: number) => ({
          id: item.id || index + 1,
          content: item.content || item.text || item.message || '',
          type: item.type || 'info'
        }));
        setNotices(formattedNotices);
      }

      // 处理联系方式数据
      if (config && config.contact) {
        setContactInfo(config.contact);
      } else if (config && config.contactInfo) {
        setContactInfo(config.contactInfo);
      }

      // 处理轮播图数据
      if (config && config.banners && Array.isArray(config.banners)) {
        const formattedBanners = config.banners.map((item: any, index: number) => ({
          id: item.id || index + 1,
          image: item.image || item.imageUrl || item.src || '',
          title: item.title || item.name || '暴风数码',
          subtitle: item.subtitle || item.description || '高价/秒/到/账',
          link: item.link || item.url || ''
        }));
        setBanners(formattedBanners);
      } else if (config && config.carousel && Array.isArray(config.carousel)) {
        // 兼容不同的字段名
        const formattedBanners = config.carousel.map((item: any, index: number) => ({
          id: item.id || index + 1,
          image: item.image || item.imageUrl || item.src || '',
          title: item.title || item.name || '暴风数码',
          subtitle: item.subtitle || item.description || '高价/秒/到/账',
          link: item.link || item.url || ''
        }));
        setBanners(formattedBanners);
      }

    } catch (error) {
      console.error('获取首页配置失败:', error);
      // 使用默认的公告数据
      setNotices([
        { id: 1, content: '📢 如有闲置手机请告知回收人员' },
        { id: 2, content: '🎉 新用户注册即享专属优惠' },
        { id: 3, content: '💰 高价回收，秒到账，安全可靠' }
      ]);

      // 使用默认的联系方式数据
      setContactInfo({
        phone: '***********',
        wechat: 'baofeng_recycle',
        shareContent: {
          title: '暴风回收 - 专业手机回收平台',
          path: '/pages/index/index',
          imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取门店数据
  const fetchStores = async () => {
    try {
      console.log('开始获取门店数据...');
      const response = await api.home.getStores();
      console.log('门店数据获取成功:', response);

      // 处理API返回的数据，根据实际接口格式 {success:true, data:{list:[...], pagination:{...}}}
      let storesData: any[] = [];
      if (response && response.success && response.data && response.data.list && Array.isArray(response.data.list)) {
        storesData = response.data.list;
      } else if (response && response.list && Array.isArray(response.list)) {
        storesData = response.list;
      }

      // 根据实际API字段映射到我们的数据结构
      const formattedStores = storesData.map((store: any) => ({
        id: store.id,
        name: store.storeName || '暴风回收门店',
        image: store.storeImage || 'https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/685552956ef735a47335a83c.jpg',
        salesPhone: store.salesPhone || store.mainPhone || '***********',
        salesWechat: store.salesWechat || store.salesPhone || '***********',
        recyclePhone: store.recyclePhone || store.mainPhone || '***********',
        recycleWechat: store.recycleWechat || store.recyclePhone || '***********',
        address: store.fullAddress || store.detailedAddress || '门店地址',
        isHot: store.businessStatus === 'OPEN' // 营业中的门店显示为热门
      }));

      console.log('格式化后的门店数据:', formattedStores);

      setStores(formattedStores);
    } catch (error) {
      console.error('获取门店数据失败:', error);
      // 使用默认的门店数据
      setStores([
        {
          id: 1,
          name: '暴风回收成都店',
          image: 'https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/685552956ef735a47335a83c.jpg',
          salesPhone: '***********',
          salesWechat: '***********',
          recyclePhone: '***********',
          recycleWechat: '***********',
          address: '四川省成都市青羊区赛格广场一楼5032-5033号',
          isHot: true
        }
      ]);
    }
  };

  // 处理手机号获取成功
  const handleGetPhoneNumber = async (e: any) => {
    console.log('手机号获取回调触发:', e);
    console.log('回调详情:', e.detail);
    try {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        console.log('用户同意获取手机号');
        Taro.showLoading({ title: '授权中...', mask: true });

        // 调用后端接口获取手机号
        try {
          const userData = await UserService.getPhoneNumber(e.detail.code);
          console.log('手机号授权成功，用户信息:', userData);

          // 更新状态
          setUserInfo(userData);
          setShowPhoneAuthModal(false);

          Taro.hideLoading();
          Taro.showToast({ title: '授权成功', icon: 'success' });
        } catch (apiError) {
          console.error('手机号授权失败:', apiError);
          Taro.hideLoading();
          Taro.showToast({ title: '授权失败，请重试', icon: 'none' });
        }
      } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
        console.log('用户拒绝获取手机号');
        Taro.showToast({ title: '需要手机号授权才能使用完整功能', icon: 'none' });
        setShowPhoneAuthModal(false);
      } else {
        console.log('获取手机号失败:', e.detail.errMsg);
        Taro.showToast({ title: '获取手机号失败', icon: 'none' });
        setShowPhoneAuthModal(false);
      }
    } catch (error) {
      console.error('手机号授权过程出错:', error);
      Taro.hideLoading();
      Taro.showToast({ title: '授权失败', icon: 'none' });
      setShowPhoneAuthModal(false);
    }
  };

  // 页面加载时获取数据和执行登录
  useEffect(() => {
    // 使用 UserService 进行自动登录
    UserService.autoLogin()
      .then((userData) => {
        console.log('登录成功，用户信息:', userData);
        setUserInfo(userData);

        // 检查是否已授权手机号
        const hasPhoneAuth = UserService.hasPhoneAuthorization();
        if (!hasPhoneAuth) {
          console.log('用户未授权手机号，显示授权提示');
          // 显示手机号授权提示
          setShowPhoneAuthModal(true);
        }

        // 登录成功后获取首页配置和门店数据
        fetchHomeConfig();
        fetchStores();
      })
      .catch((error) => {
        console.error('登录失败:', error);
        // 即使登录失败也要加载首页数据
        fetchHomeConfig();
        fetchStores();
      });
  }, []);

  // 配置分享功能 - 使用动态数据
  Taro.useShareAppMessage(() => {
    return {
      title: contactInfo.shareContent.title,
      path: contactInfo.shareContent.path,
      imageUrl: contactInfo.shareContent.imageUrl,
    };
  });

  // 配置分享到朋友圈 - 使用动态数据
  Taro.useShareTimeline(() => {
    return {
      title: contactInfo.shareContent.title || "暴风回收 - 让闲置物品变现更简单！",
      imageUrl: contactInfo.shareContent.imageUrl ||
        "https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png",
    };
  });



  // 手机价格数据
  const phonePrice = [
    { id: 1, name: "三星S25 5G S9310(价格参考)12+512", price: "-50", trend: "down" },
    { id: 2, name: "三星S25 5G S9310(价格参考)12+256", price: "-50", trend: "down" },
    { id: 3, name: "OPPO A3X 5G 6+128", price: "+20", trend: "up" },
    { id: 4, name: "OPPO A5活力版12+256", price: "-30", trend: "down" },
    { id: 5, name: "OPPO A5活力版12+512", price: "-30", trend: "down" },
  ];

  const handleSearch = (value: string) => {
    Taro.navigateTo({
      url: `/pages/search/index?keyword=${encodeURIComponent(value)}`,
    });
  };

  // 电话联系功能 - 使用动态数据
  const handlePhoneCall = () => {
    const phoneNumber = contactInfo.phone;
    Taro.showModal({
      title: '拨打电话',
      content: `确定要拨打客服电话 ${phoneNumber} 吗？`,
      success: (res) => {
        if (res.confirm) {
          Taro.makePhoneCall({
            phoneNumber
          }).catch(() => {
            Taro.showToast({ title: '拨打电话失败', icon: 'none' })
          })
        }
      },
    })
  };

  // 复制微信号功能 - 使用动态数据
  const handleCopyWechat = async () => {
    const wechatId = contactInfo.wechat;
    try {
      await Taro.setClipboardData({
        data: wechatId
      })
      Taro.showToast({ title: '微信号已复制到剪贴板', icon: 'success' })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({ title: '复制失败，请重试', icon: 'none' })
    }
  };
  const toCategory = () => {
    Taro.switchTab({
      url: `/pages/category/index`
    });
  }

  // 一键分享功能 - 使用动态数据
  const handleShare = () => {
    Taro.showActionSheet({
      itemList: ['分享小程序', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            Taro.showShareMenu({ withShareTicket: true })
            Taro.showToast({ title: '请点击右上角分享给好友', icon: 'none' })
            break
          case 1:
            const shareLink = contactInfo.shareContent.imageUrl || 'https://www.baofengrecycle.com/miniprogram';
            Taro.setClipboardData({
              data: shareLink
            }).then(() => {
              Taro.showToast({ title: '链接已复制', icon: 'success' })
            })
            break
        }
      }
    })
  };

  // 门店电话联系功能
  const handleStorePhoneCall = (phone: string) => {
    Taro.showModal({
      title: '拨打电话',
      content: `确定要拨打电话 ${phone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          Taro.makePhoneCall({
            phoneNumber: phone
          }).catch(() => {
            Taro.showToast({ title: '拨打电话失败', icon: 'none' })
          })
        }
      },
    })
  };

  // 门店微信复制功能
  const handleStoreWechatCopy = async (wechat: string) => {
    try {
      await Taro.setClipboardData({
        data: wechat
      })
      Taro.showToast({ title: '微信号已复制到剪贴板', icon: 'success' })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({ title: '复制失败，请重试', icon: 'none' })
    }
  };

  return (
    <View className="home-page">
      {/* 搜索栏 */}
      <View className="search-section">
        <SearchBar
          placeholder="搜索想要回收的商品"
          onSearch={handleSearch}
        />
      </View>

      {/* 轮播图 - 使用 Taro 原生组件 */}
      <View className="banner-section">
        <Swiper
          className="banner-swiper"
          indicatorDots
          autoplay
          interval={3000}
          duration={500}
        >
          {banners.map((banner) => (
            <SwiperItem key={banner.id}>
              <View className="banner-item">
                <Image
                  src={banner.image}
                  className="banner-image"
                  mode="aspectFill"
                />
                <View className="banner-overlay">
                  <Text className="banner-title">{banner.title}</Text>
                  <Text className="banner-subtitle">{banner.subtitle}</Text>
                </View>
              </View>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      {/* 公告栏 */}
      <View className="notice-section">
        {notices.length > 0 && (
          <NoticeBar
            text={notices.map(notice => notice.content).join('    ')}
            background="#fff7e6"
            color="#fa8c16"
            scrollable
          />
        )}
      </View>

      {/* 手机报价卡片 */}
      <View className="price-card-section">
        <View className="price-card">
          <View className="price-card-right">
            <Text className="check-price-btn" onClick={toCategory}>去查看</Text>
          </View>
        </View>
      </View>

      {/* 联系功能区域 */}
      <View className="contact-actions-section">
        <View className="contact-section-title">
          <Text className="title-text">暴风回收·当面收</Text>
        </View>
        <View className="contact-actions">
          <View className="contact-action-item" onClick={handlePhoneCall}>
            <Image
              src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E7%94%B5%E8%AF%9D%20%281%29.png.png"
              className="phone-icon"
              mode="aspectFit"
            />
            <Text className="action-text">电话联系</Text>
          </View>
          <View className="contact-action-item" onClick={handleCopyWechat}>
            <Image
              src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E5%BE%AE%E4%BF%A1%E5%8F%B7.png.png"
              className="phone-icon"
              mode="aspectFit"
            />
            <Text className="action-text">复制微信号</Text>
          </View>
          <View className="contact-action-item" onClick={handleShare}>
            <Image
              src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E5%88%86%E4%BA%AB%20%281%29.png.png"
              className="phone-icon"
              mode="aspectFit"
            />
            <Text className="action-text">一键分享</Text>
          </View>
        </View>
      </View>

      {/* 门面当面卖区域 */}
      <View className="store-section">
        <View className="store-header">
          <Text className="store-title">门面当面卖</Text>
          <View className="store-badge">HOT</View>
        </View>
        {stores.map((store) => (
          <View key={store.id} className="store-item">
            <View className="store-content">
              <View className="store-image">
                <Image
                  src={store.image}
                  className="store-photo"
                  mode="aspectFill"
                />
              </View>
              <View className="store-info">
                <View className="contact-row">
                  <Text className="contact-label">销售电话/微信：</Text>
                  <Text className="contact-number">{store.salesPhone}</Text>
                  <View className="contact-icons">
                    <View
                      className="contact-icon phone-green"
                      onClick={() => handleStorePhoneCall(store.salesPhone)}
                    >
                      <Image
                        src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E7%94%B5%E8%AF%9D%20%281%29.png.png"
                        className="icon-image"
                        mode="aspectFit"
                      />
                    </View>
                    <View
                      className="contact-icon wechat-blue"
                      onClick={() => handleStoreWechatCopy(store.salesWechat)}
                    >
                      <Image
                        src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E5%BE%AE%E4%BF%A1%E5%8F%B7.png.png"
                        className="icon-image"
                        mode="aspectFit"
                      />
                    </View>
                  </View>
                </View>
                <View className="contact-row">
                  <Text className="contact-label">回收电话/微信：</Text>
                  <Text className="contact-number">{store.recyclePhone}</Text>
                  <View className="contact-icons">
                    <View
                      className="contact-icon phone-green"
                      onClick={() => handleStorePhoneCall(store.recyclePhone)}
                    >
                      <Image
                        src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E7%94%B5%E8%AF%9D%20%281%29.png.png"
                        className="icon-image"
                        mode="aspectFit"
                      />
                    </View>
                    <View
                      className="contact-icon wechat-blue"
                      onClick={() => handleStoreWechatCopy(store.recycleWechat)}
                    >
                      <Image
                        src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E5%BE%AE%E4%BF%A1%E5%8F%B7.png.png"
                        className="icon-image"
                        mode="aspectFit"
                      />
                    </View>
                  </View>
                </View>
                <View className="address-row">
                  <Text className="address-text">{store.address}</Text>
                  <View className="location-icon" onClick={() => {}}>
                    <Image
                      src="https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E5%BE%AE%E4%BF%A1%E5%8F%B7.png.png"
                      className="icon-image"
                      mode="aspectFit"
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>

      {/* 手机价格涨跌榜 */}
      <View className="price-list-section">
        <Text className="price-list-title">视频机价格涨跌榜</Text>
        <View className="price-list">
          {phonePrice.map((phone) => (
            <View key={phone.id} className="price-item">
              <Text className="phone-name">{phone.name}</Text>
              <View className="price-change">
                <Text className={`price-trend ${phone.trend}`}>
                  {phone.trend === 'up' ? '📈' : '📉'} {phone.price}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>


    </View>
  );
}

export default Index;
